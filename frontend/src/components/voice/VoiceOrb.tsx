import { useEffect, useRef, useState } from 'react';
import { useVoiceStateVisuals, useEmotionAnimations } from '../../hooks/useEmotionVisuals';
import { blendEmotionColors, getOrbColors } from '../../utils/emotionColorMapping';

export interface VoiceOrbProps {
  emotions?: Record<string, number>;
  isListening?: boolean;
  isProcessing?: boolean;
  isSpeaking?: boolean;
  isIdle?: boolean;
  intensity?: number;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

export default function VoiceOrb({
  emotions = {},
  isListening = false,
  isProcessing = false,
  isSpeaking = false,
  isIdle = true,
  intensity = 0.5,
  size = 'large',
  className = ''
}: VoiceOrbProps) {
  const orbRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [currentState, setCurrentState] = useState<'idle' | 'listening' | 'processing' | 'speaking'>('idle');

  // Use the enhanced emotion visuals hook
  const voiceVisuals = useVoiceStateVisuals(isListening, isProcessing, isSpeaking, emotions);
  const emotionAnimations = useEmotionAnimations(emotions, !isIdle);

  // Get emotion-based colors
  const emotionColorScheme = blendEmotionColors(emotions);
  const orbColors = getOrbColors(emotionColorScheme);

  // Size configurations
  const sizeConfig = {
    small: { width: 120, height: 120, glowSize: 160 },
    medium: { width: 180, height: 180, glowSize: 240 },
    large: { width: 240, height: 240, glowSize: 320 }
  };

  const config = sizeConfig[size];

  // Update current state based on props
  useEffect(() => {
    if (isSpeaking) {
      setCurrentState('speaking');
    } else if (isProcessing) {
      setCurrentState('processing');
    } else if (isListening) {
      setCurrentState('listening');
    } else {
      setCurrentState('idle');
    }
  }, [isListening, isProcessing, isSpeaking, isIdle]);

  // Get state-specific animations and effects using the new emotion system
  const getStateEffects = () => {
    const baseIntensity = voiceVisuals.intensity * intensity;
    const animationSpeed = emotionAnimations.animationSpeed;

    switch (currentState) {
      case 'listening':
        return {
          scale: emotionAnimations.scaleVariation * (1.01 + (baseIntensity * 0.04)),
          pulseSpeed: `${4 / animationSpeed}s`,
          glowIntensity: voiceVisuals.pulseIntensity * 0.5,
          rotationSpeed: `${15 / animationSpeed}s`,
          innerGlow: true,
          pulseRings: true,
          glowRadius: voiceVisuals.glowRadius
        };
      case 'processing':
        return {
          scale: emotionAnimations.scaleVariation * (1.0 + (baseIntensity * 0.03)),
          pulseSpeed: `${5 / animationSpeed}s`,
          glowIntensity: voiceVisuals.pulseIntensity * 0.4,
          rotationSpeed: `${12 / animationSpeed}s`,
          innerGlow: true,
          swirling: true,
          glowRadius: voiceVisuals.glowRadius
        };
      case 'speaking':
        return {
          scale: emotionAnimations.scaleVariation * (1.02 + (baseIntensity * 0.06)),
          pulseSpeed: `${3.5 / animationSpeed}s`,
          glowIntensity: voiceVisuals.pulseIntensity * 0.6,
          rotationSpeed: `${14 / animationSpeed}s`,
          innerGlow: true,
          expanding: false,
          glowRadius: voiceVisuals.glowRadius
        };
      default: // idle
        return {
          scale: emotionAnimations.scaleVariation,
          pulseSpeed: `${6 / animationSpeed}s`,
          glowIntensity: voiceVisuals.pulseIntensity * 0.3,
          rotationSpeed: `${18 / animationSpeed}s`,
          innerGlow: false,
          gentle: true,
          glowRadius: voiceVisuals.glowRadius * 0.5
        };
    }
  };

  const effects = getStateEffects();

  return (
    <div 
      className={`relative flex items-center justify-center ${className}`}
      style={{ 
        width: config.glowSize, 
        height: config.glowSize 
      }}
    >
      {/* Otherworldly Light Wave Emissions */}
      {(isListening || isProcessing || isSpeaking) && (
        <>
          {/* Massive outer energy field */}
          <div
            className="absolute rounded-full animate-ping"
            style={{
              width: config.glowSize * 2.5,
              height: config.glowSize * 2.5,
              background: `radial-gradient(circle,
                rgba(147, 197, 253, ${effects.glowIntensity * 0.15}) 0%,
                rgba(196, 181, 253, ${effects.glowIntensity * 0.1}) 30%,
                transparent 70%
              )`,
              animationDuration: `${3 / emotionAnimations.animationSpeed}s`,
              animationDelay: '0s',
              filter: 'blur(8px)'
            }}
          />

          {/* Secondary energy wave */}
          <div
            className="absolute rounded-full animate-ping"
            style={{
              width: config.glowSize * 2,
              height: config.glowSize * 2,
              background: `radial-gradient(circle,
                rgba(165, 243, 252, ${effects.glowIntensity * 0.2}) 0%,
                rgba(147, 197, 253, ${effects.glowIntensity * 0.15}) 40%,
                transparent 80%
              )`,
              animationDuration: `${2.5 / emotionAnimations.animationSpeed}s`,
              animationDelay: '0.5s',
              filter: 'blur(6px)'
            }}
          />

          {/* Primary energy ring */}
          <div
            className="absolute rounded-full animate-ping"
            style={{
              width: config.glowSize * 1.5,
              height: config.glowSize * 1.5,
              background: `radial-gradient(circle,
                rgba(251, 207, 232, ${effects.glowIntensity * 0.3}) 0%,
                rgba(196, 181, 253, ${effects.glowIntensity * 0.2}) 50%,
                transparent 90%
              )`,
              animationDuration: `${2 / emotionAnimations.animationSpeed}s`,
              animationDelay: '1s',
              filter: 'blur(4px)'
            }}
          />

          {/* Inner intense glow */}
          <div
            className="absolute rounded-full animate-ping"
            style={{
              width: config.glowSize * 1.2,
              height: config.glowSize * 1.2,
              background: `radial-gradient(circle,
                rgba(147, 197, 253, ${effects.glowIntensity * 0.4}) 0%,
                rgba(165, 243, 252, ${effects.glowIntensity * 0.3}) 60%,
                transparent 100%
              )`,
              animationDuration: `${1.5 / emotionAnimations.animationSpeed}s`,
              animationDelay: '0.2s',
              filter: 'blur(2px)'
            }}
          />
        </>
      )}

      {/* Continuous ambient glow for all states */}
      <div
        className="absolute rounded-full"
        style={{
          width: config.glowSize * 1.8,
          height: config.glowSize * 1.8,
          background: `radial-gradient(circle,
            rgba(147, 197, 253, ${0.1 + effects.glowIntensity * 0.1}) 0%,
            rgba(196, 181, 253, ${0.08 + effects.glowIntensity * 0.08}) 40%,
            rgba(251, 207, 232, ${0.05 + effects.glowIntensity * 0.05}) 70%,
            transparent 100%
          )`,
          filter: 'blur(12px)',
          animation: `orb-ambient-glow ${8 / emotionAnimations.animationSpeed}s ease-in-out infinite`
        }}
      />

      {/* Main 3D Sphere Container */}
      <div
        ref={orbRef}
        className="relative transition-all duration-500 ease-out"
        style={{
          width: config.width,
          height: config.height,
          transform: `scale(${effects.scale})`,
          transformStyle: 'preserve-3d',
          perspective: '1000px',
          filter: `
            drop-shadow(0 0 ${60 * effects.glowIntensity}px rgba(147, 197, 253, ${effects.glowIntensity * 0.8}))
            drop-shadow(0 0 ${40 * effects.glowIntensity}px rgba(196, 181, 253, ${effects.glowIntensity * 0.6}))
            drop-shadow(0 0 ${80 * effects.glowIntensity}px rgba(165, 243, 252, ${effects.glowIntensity * 0.4}))
            drop-shadow(0 0 ${20 * effects.glowIntensity}px rgba(251, 207, 232, ${effects.glowIntensity * 0.7}))
          `
        }}
      >
        {/* 3D Sphere Base with Fluid Distortion */}
        <div
          className="absolute inset-0 transition-all duration-1000 ease-out"
          style={{
            width: '100%',
            height: '100%',
            borderRadius: '50%',
            background: `
              radial-gradient(ellipse at 25% 25%,
                ${orbColors.primary.replace('rgb', 'rgba').replace(')', ', 0.95)')} 0%,
                ${orbColors.secondary.replace('rgb', 'rgba').replace(')', ', 0.85)')} 30%,
                ${orbColors.primary.replace('rgb', 'rgba').replace(')', ', 0.75)')} 60%,
                ${orbColors.secondary.replace('rgb', 'rgba').replace(')', ', 0.65)')} 85%,
                ${orbColors.primary.replace('rgb', 'rgba').replace(')', ', 0.55)')} 100%
              )
            `,
            opacity: 0.9 + (voiceVisuals.intensity * 0.1),
            animation: `sphere-fluid-rotation ${effects.rotationSpeed} linear infinite, sphere-morph 8s ease-in-out infinite`,
            filter: `brightness(${1.2 + emotionAnimations.colorIntensity * 0.4}) saturate(${1.6 + voiceVisuals.intensity * 0.8}) blur(0.3px)`,
            backdropFilter: 'blur(12px)',
            transform: 'rotateX(15deg) rotateY(0deg)',
            boxShadow: `
              inset 0 0 ${40 * effects.glowIntensity}px rgba(255, 255, 255, 0.3),
              inset ${-20 * effects.glowIntensity}px ${-20 * effects.glowIntensity}px ${60 * effects.glowIntensity}px rgba(0, 0, 0, 0.2),
              0 0 ${80 * effects.glowIntensity}px rgba(147, 197, 253, ${effects.glowIntensity * 0.4})
            `
          }}
        />

        {/* Fluid Wave Distortion Layer */}
        <div
          className="absolute inset-0 transition-all duration-1000 ease-out"
          style={{
            width: '100%',
            height: '100%',
            borderRadius: '50%',
            background: `
              conic-gradient(from 0deg at 50% 50%,
                rgba(147, 197, 253, 0.7) 0deg,
                rgba(196, 181, 253, 0.8) 45deg,
                rgba(251, 207, 232, 0.9) 90deg,
                rgba(165, 243, 252, 0.8) 135deg,
                rgba(147, 197, 253, 0.7) 180deg,
                rgba(196, 181, 253, 0.8) 225deg,
                rgba(251, 207, 232, 0.9) 270deg,
                rgba(165, 243, 252, 0.8) 315deg,
                rgba(147, 197, 253, 0.7) 360deg
              )
            `,
            opacity: 0.7 + (voiceVisuals.intensity * 0.3),
            animation: `sphere-wave-distortion ${effects.rotationSpeed} linear infinite reverse, sphere-pulse 4s ease-in-out infinite`,
            filter: `brightness(${1.3 + emotionAnimations.colorIntensity * 0.3}) saturate(1.8) blur(0.5px)`,
            transform: 'rotateX(10deg) rotateY(0deg)',
            clipPath: 'ellipse(48% 52% at 50% 50%)'
          }}
        />



        {/* 3D Sphere Top Highlight */}
        <div
          className="absolute inset-0"
          style={{
            width: '100%',
            height: '100%',
            borderRadius: '50%',
            background: `
              radial-gradient(ellipse at 20% 20%,
                rgba(255, 255, 255, 0.9) 0%,
                rgba(255, 255, 255, 0.6) 12%,
                rgba(255, 255, 255, 0.3) 25%,
                rgba(255, 255, 255, 0.1) 40%,
                transparent 55%
              )
            `,
            filter: 'blur(0.3px)',
            transform: 'rotateX(20deg) rotateY(-10deg)',
            animation: `sphere-highlight-shift 6s ease-in-out infinite`
          }}
        />

        {/* 3D Sphere Rim Light */}
        <div
          className="absolute inset-0"
          style={{
            width: '100%',
            height: '100%',
            borderRadius: '50%',
            background: `
              radial-gradient(circle at 50% 50%,
                transparent 65%,
                rgba(255, 255, 255, 0.5) 75%,
                rgba(255, 255, 255, 0.8) 85%,
                rgba(255, 255, 255, 0.4) 95%,
                transparent 100%
              )
            `,
            filter: 'blur(0.5px)',
            animation: `sphere-rim-glow 5s ease-in-out infinite`
          }}
        />

        {/* 3D Depth Shadow */}
        <div
          className="absolute inset-0"
          style={{
            width: '100%',
            height: '100%',
            borderRadius: '50%',
            background: `
              radial-gradient(ellipse at 50% 80%,
                rgba(59, 130, 246, 0.4) 0%,
                rgba(59, 130, 246, 0.2) 25%,
                rgba(147, 51, 234, 0.15) 45%,
                rgba(0, 0, 0, 0.1) 65%,
                transparent 80%
              )
            `,
            transform: 'rotateX(-10deg)',
            filter: 'blur(1px)'
          }}
        />

        {/* Fluid Surface Waves */}
        <div
          className="absolute inset-0"
          style={{
            width: '100%',
            height: '100%',
            borderRadius: '50%',
            background: `
              linear-gradient(45deg,
                transparent 0%,
                rgba(255, 255, 255, 0.2) 20%,
                transparent 40%,
                rgba(255, 255, 255, 0.15) 60%,
                transparent 80%,
                rgba(255, 255, 255, 0.1) 100%
              )
            `,
            opacity: 0.6 + (voiceVisuals.intensity * 0.4),
            animation: `sphere-surface-waves 7s ease-in-out infinite`,
            filter: 'blur(0.8px)',
            clipPath: 'ellipse(45% 48% at 50% 50%)'
          }}
        />

        {/* Inner glow for active states */}
        {effects.innerGlow && (
          <div
            className="absolute inset-1 rounded-full animate-pulse"
            style={{
              background: `
                radial-gradient(circle at 50% 50%,
                  rgba(147, 197, 253, 0.4) 0%,
                  rgba(196, 181, 253, 0.3) 50%,
                  transparent 100%
                )
              `,
              opacity: 0.5 * effects.glowIntensity,
              animationDuration: effects.pulseSpeed,
              filter: 'blur(2px)'
            }}
          />
        )}

        {/* Swirling effect for processing */}
        {effects.swirling && (
          <div
            className="absolute inset-0 rounded-full"
            style={{
              background: `
                conic-gradient(from 0deg,
                  transparent 0deg,
                  rgba(147, 197, 253, 0.3) 90deg,
                  rgba(196, 181, 253, 0.4) 180deg,
                  rgba(251, 207, 232, 0.3) 270deg,
                  transparent 360deg
                )
              `,
              animation: `orb-swirl 2s linear infinite`,
              filter: 'blur(1px)'
            }}
          />
        )}

        {/* Expanding rings for speaking */}
        {effects.expanding && (
          <>
            {/* Massive outer speech wave */}
            <div
              className="absolute rounded-full animate-ping"
              style={{
                width: '150%',
                height: '150%',
                top: '-25%',
                left: '-25%',
                background: `radial-gradient(circle,
                  transparent 60%,
                  rgba(147, 197, 253, 0.3) 70%,
                  rgba(147, 197, 253, 0.1) 85%,
                  transparent 100%
                )`,
                animationDuration: '0.6s',
                filter: 'blur(2px)'
              }}
            />

            {/* Medium speech wave */}
            <div
              className="absolute rounded-full animate-ping"
              style={{
                width: '130%',
                height: '130%',
                top: '-15%',
                left: '-15%',
                background: `radial-gradient(circle,
                  transparent 50%,
                  rgba(196, 181, 253, 0.4) 65%,
                  rgba(196, 181, 253, 0.2) 80%,
                  transparent 100%
                )`,
                animationDuration: '0.8s',
                animationDelay: '0.1s',
                filter: 'blur(1.5px)'
              }}
            />

            {/* Inner speech wave */}
            <div
              className="absolute rounded-full animate-ping"
              style={{
                width: '120%',
                height: '120%',
                top: '-10%',
                left: '-10%',
                background: `radial-gradient(circle,
                  transparent 40%,
                  rgba(251, 207, 232, 0.5) 60%,
                  rgba(251, 207, 232, 0.3) 75%,
                  transparent 100%
                )`,
                animationDuration: '1.0s',
                animationDelay: '0.2s',
                filter: 'blur(1px)'
              }}
            />
          </>
        )}
      </div>

      {/* 3D Floating Elements for Enhanced Otherworldly Effect */}
      {(isSpeaking || isProcessing || isListening) && (
        <div className="absolute inset-0 pointer-events-none overflow-hidden" style={{ perspective: '1000px' }}>
          {[...Array(12)].map((_, i) => {
            const colors = [
              'rgba(147, 197, 253, 0.8)',
              'rgba(196, 181, 253, 0.9)',
              'rgba(251, 207, 232, 0.7)',
              'rgba(165, 243, 252, 0.8)',
              'rgba(255, 255, 255, 0.6)'
            ];
            const size = 3 + (i % 4);
            const isLarge = i < 4;
            return (
              <div
                key={i}
                className="absolute rounded-full"
                style={{
                  width: `${size}px`,
                  height: `${size}px`,
                  background: `radial-gradient(circle, ${colors[i % colors.length]} 0%, transparent 70%)`,
                  left: `${10 + (i * 7)}%`,
                  top: `${20 + (i * 6)}%`,
                  filter: `blur(${isLarge ? '0.3px' : '0.8px'})`,
                  boxShadow: `
                    0 0 ${8 + (i % 3) * 4}px ${colors[i % colors.length]},
                    inset 0 0 ${4 + (i % 2) * 2}px rgba(255, 255, 255, 0.3)
                  `,
                  animation: `
                    float ${2.5 + (i * 0.4)}s ease-in-out infinite,
                    sphere-3d-orbit-${i % 3} ${8 + (i * 0.5)}s linear infinite
                  `,
                  animationDelay: `${i * 0.3}s`,
                  transform: `translateZ(${isLarge ? '20px' : '10px'})`,
                  transformStyle: 'preserve-3d'
                }}
              />
            );
          })}

          {/* Larger 3D Orbital Elements */}
          {[...Array(6)].map((_, i) => (
            <div
              key={`orbital-${i}`}
              className="absolute rounded-full"
              style={{
                width: `${8 + (i % 3) * 2}px`,
                height: `${8 + (i % 3) * 2}px`,
                background: `
                  radial-gradient(circle at 30% 30%,
                    rgba(255, 255, 255, 0.9) 0%,
                    rgba(147, 197, 253, 0.7) 40%,
                    rgba(196, 181, 253, 0.5) 80%,
                    transparent 100%
                  )
                `,
                left: `${20 + (i * 12)}%`,
                top: `${30 + (i * 8)}%`,
                filter: 'blur(0.2px)',
                boxShadow: `
                  0 0 ${15 + (i % 2) * 5}px rgba(147, 197, 253, 0.6),
                  inset 0 0 ${6}px rgba(255, 255, 255, 0.4)
                `,
                animation: `
                  sphere-3d-orbit-large ${12 + (i * 0.8)}s linear infinite,
                  sphere-glow-pulse ${4 + (i * 0.3)}s ease-in-out infinite
                `,
                animationDelay: `${i * 0.5}s`,
                transform: `translateZ(${30 + (i * 5)}px) rotateX(${i * 15}deg)`,
                transformStyle: 'preserve-3d'
              }}
            />
          ))}
        </div>
      )}

      <style>{`
        @keyframes sphere-fluid-rotation {
          0% { transform: rotateX(15deg) rotateY(0deg) rotateZ(0deg); }
          25% { transform: rotateX(20deg) rotateY(90deg) rotateZ(5deg); }
          50% { transform: rotateX(10deg) rotateY(180deg) rotateZ(-3deg); }
          75% { transform: rotateX(25deg) rotateY(270deg) rotateZ(8deg); }
          100% { transform: rotateX(15deg) rotateY(360deg) rotateZ(0deg); }
        }

        @keyframes sphere-wave-distortion {
          0% {
            transform: rotateX(10deg) rotateY(0deg);
            clip-path: ellipse(48% 52% at 50% 50%);
          }
          25% {
            transform: rotateX(15deg) rotateY(90deg);
            clip-path: ellipse(52% 48% at 45% 55%);
          }
          50% {
            transform: rotateX(5deg) rotateY(180deg);
            clip-path: ellipse(46% 54% at 55% 45%);
          }
          75% {
            transform: rotateX(20deg) rotateY(270deg);
            clip-path: ellipse(50% 50% at 50% 50%);
          }
          100% {
            transform: rotateX(10deg) rotateY(360deg);
            clip-path: ellipse(48% 52% at 50% 50%);
          }
        }

        @keyframes sphere-morph {
          0%, 100% {
            border-radius: 50%;
            transform: rotateX(15deg) rotateY(0deg) scale(1);
          }
          25% {
            border-radius: 48% 52% 50% 50%;
            transform: rotateX(18deg) rotateY(0deg) scale(1.02);
          }
          50% {
            border-radius: 52% 48% 46% 54%;
            transform: rotateX(12deg) rotateY(0deg) scale(0.98);
          }
          75% {
            border-radius: 50% 50% 52% 48%;
            transform: rotateX(22deg) rotateY(0deg) scale(1.01);
          }
        }

        @keyframes sphere-pulse {
          0%, 100% {
            opacity: 0.7;
            transform: rotateX(10deg) rotateY(0deg) scale(1);
          }
          50% {
            opacity: 0.9;
            transform: rotateX(15deg) rotateY(0deg) scale(1.05);
          }
        }

        @keyframes sphere-highlight-shift {
          0%, 100% {
            transform: rotateX(20deg) rotateY(-10deg);
            opacity: 0.8;
          }
          33% {
            transform: rotateX(25deg) rotateY(5deg);
            opacity: 0.9;
          }
          66% {
            transform: rotateX(15deg) rotateY(10deg);
            opacity: 0.7;
          }
        }

        @keyframes sphere-rim-glow {
          0%, 100% {
            opacity: 0.6;
            filter: blur(0.5px);
          }
          50% {
            opacity: 0.9;
            filter: blur(0.3px);
          }
        }

        @keyframes sphere-surface-waves {
          0%, 100% {
            transform: rotateZ(0deg);
            opacity: 0.6;
          }
          25% {
            transform: rotateZ(5deg);
            opacity: 0.8;
          }
          50% {
            transform: rotateZ(-3deg);
            opacity: 0.7;
          }
          75% {
            transform: rotateZ(8deg);
            opacity: 0.9;
          }
        }

        @keyframes orb-rotation {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        @keyframes orb-swirl {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        @keyframes orb-ambient-glow {
          0%, 100% {
            opacity: 0.6;
            transform: scale(1) rotate(0deg);
          }
          25% {
            opacity: 0.8;
            transform: scale(1.05) rotate(90deg);
          }
          50% {
            opacity: 1;
            transform: scale(1.1) rotate(180deg);
          }
          75% {
            opacity: 0.9;
            transform: scale(1.05) rotate(270deg);
          }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px) translateX(0px) scale(1) rotate(0deg);
            opacity: 0.3;
          }
          20% {
            transform: translateY(-15px) translateX(8px) scale(1.1) rotate(72deg);
            opacity: 0.7;
          }
          40% {
            transform: translateY(-25px) translateX(-3px) scale(0.9) rotate(144deg);
            opacity: 1;
          }
          60% {
            transform: translateY(-30px) translateX(12px) scale(1.2) rotate(216deg);
            opacity: 0.8;
          }
          80% {
            transform: translateY(-15px) translateX(-8px) scale(0.8) rotate(288deg);
            opacity: 0.5;
          }
        }

        @keyframes sphere-3d-orbit-0 {
          0% {
            transform: translateZ(10px) rotateX(0deg) rotateY(0deg) translateX(0px) translateY(0px);
          }
          25% {
            transform: translateZ(15px) rotateX(90deg) rotateY(90deg) translateX(20px) translateY(-10px);
          }
          50% {
            transform: translateZ(20px) rotateX(180deg) rotateY(180deg) translateX(0px) translateY(-20px);
          }
          75% {
            transform: translateZ(15px) rotateX(270deg) rotateY(270deg) translateX(-20px) translateY(-10px);
          }
          100% {
            transform: translateZ(10px) rotateX(360deg) rotateY(360deg) translateX(0px) translateY(0px);
          }
        }

        @keyframes sphere-3d-orbit-1 {
          0% {
            transform: translateZ(15px) rotateX(45deg) rotateY(0deg) translateX(15px) translateY(15px);
          }
          25% {
            transform: translateZ(25px) rotateX(135deg) rotateY(90deg) translateX(-15px) translateY(15px);
          }
          50% {
            transform: translateZ(30px) rotateX(225deg) rotateY(180deg) translateX(-15px) translateY(-15px);
          }
          75% {
            transform: translateZ(25px) rotateX(315deg) rotateY(270deg) translateX(15px) translateY(-15px);
          }
          100% {
            transform: translateZ(15px) rotateX(405deg) rotateY(360deg) translateX(15px) translateY(15px);
          }
        }

        @keyframes sphere-3d-orbit-2 {
          0% {
            transform: translateZ(20px) rotateX(30deg) rotateY(60deg) translateX(-10px) translateY(25px);
          }
          25% {
            transform: translateZ(10px) rotateX(120deg) rotateY(150deg) translateX(-25px) translateY(-10px);
          }
          50% {
            transform: translateZ(25px) rotateX(210deg) rotateY(240deg) translateX(10px) translateY(-25px);
          }
          75% {
            transform: translateZ(15px) rotateX(300deg) rotateY(330deg) translateX(25px) translateY(10px);
          }
          100% {
            transform: translateZ(20px) rotateX(390deg) rotateY(420deg) translateX(-10px) translateY(25px);
          }
        }

        @keyframes sphere-3d-orbit-large {
          0% {
            transform: translateZ(30px) rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateX(0px) translateY(0px);
          }
          20% {
            transform: translateZ(45px) rotateX(72deg) rotateY(72deg) rotateZ(36deg) translateX(30px) translateY(-15px);
          }
          40% {
            transform: translateZ(60px) rotateX(144deg) rotateY(144deg) rotateZ(72deg) translateX(15px) translateY(-30px);
          }
          60% {
            transform: translateZ(45px) rotateX(216deg) rotateY(216deg) rotateZ(108deg) translateX(-30px) translateY(-15px);
          }
          80% {
            transform: translateZ(35px) rotateX(288deg) rotateY(288deg) rotateZ(144deg) translateX(-15px) translateY(30px);
          }
          100% {
            transform: translateZ(30px) rotateX(360deg) rotateY(360deg) rotateZ(180deg) translateX(0px) translateY(0px);
          }
        }

        @keyframes sphere-glow-pulse {
          0%, 100% {
            opacity: 0.6;
            filter: blur(0.2px);
            box-shadow: 0 0 15px rgba(147, 197, 253, 0.6), inset 0 0 6px rgba(255, 255, 255, 0.4);
          }
          50% {
            opacity: 1;
            filter: blur(0.1px);
            box-shadow: 0 0 25px rgba(147, 197, 253, 0.9), inset 0 0 10px rgba(255, 255, 255, 0.6);
          }
        }
      `}</style>
    </div>
  );
}
